#ifndef ALLTOOLS_H
#define ALLTOOLS_H

/**
 * @brief 所有工具的便利包含头文件
 * 
 * 这个文件包含了所有可用的图形工具，方便统一管理和使用。
 * 新增工具时，只需要在这里添加对应的包含即可。
 */

// 基础工具
#include "basic/FreeDrawTool.h"
#include "basic/FreeDrawDashedTool.h"
#include "basic/FreeDrawHighlighterTool.h"
#include "basic/LineTool.h"
#include "basic/DashedLineTool.h"

// 增强工具
#include "enhanced/EnhancedFreeDrawTool.h"

// 几何形状工具
#include "shapes/RectangleTool.h"
#include "shapes/SquareTool.h"
#include "shapes/EllipseTool.h"
#include "shapes/CircleTool.h"
#include "shapes/TriangleTool.h"
#include "shapes/RightTriangleTool.h"

// 特殊工具
#include "special/ArrowTool.h"
#include "special/ImageTool.h"

// 工具管理器
#include "ShapeToolManager.h"

/**
 * @brief 一键注册所有基础工具的便利函数
 * 
 * 使用示例：
 * registerAllBasicTools();
 */
inline void registerAllBasicTools(ShapeToolManager* manager)
{
    // 直接使用传入的manager，避免循环调用
    if (!manager) {
        return;
    }

    // 基础工具
    manager->registerTool(new FreeDrawTool());
    manager->registerTool(new FreeDrawDashedTool());
    manager->registerTool(new FreeDrawHighlighterTool());
    manager->registerTool(new EnhancedFreeDrawTool());  // 增强笔锋工具
    manager->registerTool(new LineTool());
    manager->registerTool(new DashedLineTool());

    // 几何形状工具
    manager->registerTool(new RectangleTool());
    manager->registerTool(new SquareTool());
    manager->registerTool(new EllipseTool());
    manager->registerTool(new CircleTool());
    manager->registerTool(new TriangleTool());
    manager->registerTool(new RightTriangleTool());

    // 特殊工具
    manager->registerTool(new ArrowTool());
    manager->registerTool(new ImageTool());
}

#endif // ALLTOOLS_H
