#ifndef BRUSHSTROKEBENCHMARK_H
#define BRUSHSTROKEBENCHMARK_H

#include <QObject>
#include <QTest>
#include <QElapsedTimer>
#include <QPainter>
#include <QPixmap>
#include "../brush/BrushStrokeRenderer.h"
#include "../tools/enhanced/EnhancedFreeDrawTool.h"
#include "../optimization/OptimizedDrawingState.h"

/**
 * @brief 笔锋功能基准测试和验证
 * 
 * 测试内容：
 * 1. 笔锋渲染性能测试
 * 2. 动态线宽计算准确性
 * 3. 路径构建效率
 * 4. 内存使用情况
 * 5. 与现有系统的兼容性
 */
class BrushStrokeBenchmark : public QObject
{
    Q_OBJECT

public:
    explicit BrushStrokeBenchmark(QObject* parent = nullptr);

private slots:
    // 基础功能测试
    void testBrushStrokeRenderer();
    void testEnhancedFreeDrawTool();
    void testOptimizedDrawingStateIntegration();
    
    // 性能测试
    void benchmarkBrushStrokeRendering();
    void benchmarkPathBuilding();
    void benchmarkMemoryUsage();
    
    // 质量测试
    void testDynamicWidthCalculation();
    void testVelocityCalculation();
    void testSmoothingAlgorithm();
    
    // 兼容性测试
    void testFeatheringIntegration();
    void testToolRegistration();

private:
    // 测试辅助方法
    QVector<QPointF> generateTestPath(int pointCount, const QRectF& bounds);
    QVector<BrushStrokeRenderer::BrushPoint> generateBrushPoints(int count);
    void measureRenderingPerformance(const QPainterPath& path, int iterations = 1000);
    void verifyPathQuality(const QPainterPath& path);
    
    // 测试数据
    BrushStrokeRenderer m_renderer;
    EnhancedFreeDrawTool m_tool;
    OptimizedDrawingState m_drawingState;
    
    // 性能基准
    static constexpr int BENCHMARK_ITERATIONS = 1000;
    static constexpr int MAX_ACCEPTABLE_RENDER_TIME_MS = 16; // 60fps
    static constexpr int MAX_POINTS_PER_STROKE = 1000;
};

// 内联实现简单的测试方法
inline BrushStrokeBenchmark::BrushStrokeBenchmark(QObject* parent)
    : QObject(parent)
{
    // 设置测试环境
    BrushStrokeRenderer::BrushConfig config;
    config.minWidth = 2.0;
    config.maxWidth = 20.0;
    config.velocityFactor = 0.8;
    config.pressureFactor = 0.6;
    config.smoothingFactor = 0.7;
    config.enableFeathering = true;
    
    m_renderer.setBrushConfig(config);
    m_tool.setBrushConfig(config);
}

inline QVector<QPointF> BrushStrokeBenchmark::generateTestPath(int pointCount, const QRectF& bounds)
{
    QVector<QPointF> points;
    points.reserve(pointCount);
    
    // 生成螺旋形路径
    qreal centerX = bounds.center().x();
    qreal centerY = bounds.center().y();
    qreal maxRadius = qMin(bounds.width(), bounds.height()) / 2.0 * 0.8;
    
    for (int i = 0; i < pointCount; ++i) {
        qreal t = static_cast<qreal>(i) / pointCount;
        qreal angle = t * 6 * M_PI; // 3圈螺旋
        qreal radius = maxRadius * t;
        
        qreal x = centerX + radius * qCos(angle);
        qreal y = centerY + radius * qSin(angle);
        
        points.append(QPointF(x, y));
    }
    
    return points;
}

inline QVector<BrushStrokeRenderer::BrushPoint> BrushStrokeBenchmark::generateBrushPoints(int count)
{
    QVector<BrushStrokeRenderer::BrushPoint> points;
    points.reserve(count);
    
    QVector<QPointF> path = generateTestPath(count, QRectF(0, 0, 800, 600));
    
    for (int i = 0; i < path.size(); ++i) {
        BrushStrokeRenderer::BrushPoint brushPoint;
        brushPoint.position = path[i];
        brushPoint.pressure = 0.5 + 0.5 * qSin(i * 0.1); // 变化的压感
        brushPoint.velocity = 50 + 30 * qCos(i * 0.05);   // 变化的速度
        brushPoint.timestamp = i * 16; // 模拟60fps
        brushPoint.width = 5.0 + 3.0 * brushPoint.pressure;
        
        points.append(brushPoint);
    }
    
    return points;
}

inline void BrushStrokeBenchmark::measureRenderingPerformance(const QPainterPath& path, int iterations)
{
    QPixmap testPixmap(800, 600);
    testPixmap.fill(Qt::white);
    
    QPen testPen(Qt::black, 5.0);
    QBrush testBrush(Qt::black);
    
    QElapsedTimer timer;
    timer.start();
    
    for (int i = 0; i < iterations; ++i) {
        QPainter painter(&testPixmap);
        painter.setRenderHint(QPainter::Antialiasing);
        
        BrushStrokeRenderer::BrushConfig config;
        BrushStrokeRenderer::drawBrushStrokeWithFeathering(
            &painter, path, testPen, testBrush, config, ToolType::EnhancedFreeDraw);
    }
    
    qint64 elapsed = timer.elapsed();
    qreal avgTime = static_cast<qreal>(elapsed) / iterations;
    
    qDebug() << "平均渲染时间:" << avgTime << "ms";
    qDebug() << "FPS等效:" << (1000.0 / avgTime);
    
    QVERIFY2(avgTime < MAX_ACCEPTABLE_RENDER_TIME_MS, 
             QString("渲染时间过长: %1ms > %2ms").arg(avgTime).arg(MAX_ACCEPTABLE_RENDER_TIME_MS).toUtf8());
}

inline void BrushStrokeBenchmark::verifyPathQuality(const QPainterPath& path)
{
    QVERIFY(!path.isEmpty());
    
    QRectF bounds = path.boundingRect();
    QVERIFY(bounds.width() > 0 && bounds.height() > 0);
    
    // 验证路径的连续性
    int elementCount = path.elementCount();
    QVERIFY(elementCount > 0);
    
    if (elementCount > 1) {
        QPainterPath::Element firstElement = path.elementAt(0);
        QVERIFY(firstElement.type == QPainterPath::MoveToElement);
    }
}

#endif // BRUSHSTROKEBENCHMARK_H
