#ifndef ENHANCEDFREEDRAWTOOL_H
#define ENHANCEDFREEDRAWTOOL_H

#include "../basic/FreeDrawTool.h"
#include "../../brush/BrushStrokeRenderer.h"

/**
 * @brief 增强版自由绘制工具 - 支持笔锋效果
 * 
 * 特点：
 * - 继承现有FreeDrawTool的所有功能
 * - 添加基于速度和压感的笔锋效果
 * - 完全复用现有的绘制流程和性能优化
 * - 与FeatheringRenderer完美集成
 */
class EnhancedFreeDrawTool : public FreeDrawTool
{
public:
    EnhancedFreeDrawTool();
    ~EnhancedFreeDrawTool() = default;
    
    // 重写基类方法以支持笔锋
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
    
    // 笔锋特有接口
    void setBrushConfig(const BrushStrokeRenderer::BrushConfig& config);
    BrushStrokeRenderer::BrushConfig getBrushConfig() const;
    
    // 笔锋路径构建接口（供OptimizedDrawingState使用）
    void startBrushStroke(const QPointF& startPoint, qreal pressure = 1.0);
    void addBrushPoint(const QPointF& point, qreal pressure = 1.0);
    void finishBrushStroke();
    void cancelBrushStroke();
    
    // 获取笔锋路径
    QPainterPath getCurrentBrushPath() const;
    QRectF getCurrentBrushBounds() const;
    bool hasBrushStroke() const;
    
    // 设置基础画笔（用于笔锋计算）
    void setBasePen(const QPen& pen);

private:
    BrushStrokeRenderer m_brushRenderer;
    bool m_brushModeEnabled = true;  // 是否启用笔锋模式
    
    // 默认笔锋配置
    static BrushStrokeRenderer::BrushConfig getDefaultBrushConfig();
};

#endif // ENHANCEDFREEDRAWTOOL_H
