#include "EnhancedFreeDrawTool.h"

EnhancedFreeDrawTool::EnhancedFreeDrawTool() 
    : FreeDrawTool()  // 继承基类构造
{
    // 设置默认笔锋配置
    m_brushRenderer.setBrushConfig(getDefaultBrushConfig());
}

QPainterPath EnhancedFreeDrawTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    if (m_brushModeEnabled && m_brushRenderer.hasStroke()) {
        // 笔锋模式：返回笔锋路径
        return m_brushRenderer.getCurrentStrokePath();
    } else {
        // 回退到基类实现
        return FreeDrawTool::createPath(startPoint, currentPoint);
    }
}

QRectF EnhancedFreeDrawTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    if (m_brushModeEnabled && m_brushRenderer.hasStroke()) {
        // 笔锋模式：返回笔锋边界
        return m_brushRenderer.getCurrentBounds();
    } else {
        // 回退到基类实现
        return FreeDrawTool::getBoundingRect(startPoint, currentPoint);
    }
}

QString EnhancedFreeDrawTool::getToolName() const
{
    return "EnhancedFreeDraw";
}

void EnhancedFreeDrawTool::setBrushConfig(const BrushStrokeRenderer::BrushConfig& config)
{
    m_brushRenderer.setBrushConfig(config);
}

BrushStrokeRenderer::BrushConfig EnhancedFreeDrawTool::getBrushConfig() const
{
    return m_brushRenderer.getBrushConfig();
}

void EnhancedFreeDrawTool::startBrushStroke(const QPointF& startPoint, qreal pressure)
{
    if (m_brushModeEnabled) {
        m_brushRenderer.startStroke(startPoint, pressure);
    }
}

void EnhancedFreeDrawTool::addBrushPoint(const QPointF& point, qreal pressure)
{
    if (m_brushModeEnabled) {
        m_brushRenderer.addStrokePoint(point, pressure);
    }
}

void EnhancedFreeDrawTool::finishBrushStroke()
{
    if (m_brushModeEnabled) {
        m_brushRenderer.finishStroke();
    }
}

void EnhancedFreeDrawTool::cancelBrushStroke()
{
    if (m_brushModeEnabled) {
        m_brushRenderer.cancelStroke();
    }
}

QPainterPath EnhancedFreeDrawTool::getCurrentBrushPath() const
{
    return m_brushRenderer.getCurrentStrokePath();
}

QRectF EnhancedFreeDrawTool::getCurrentBrushBounds() const
{
    return m_brushRenderer.getCurrentBounds();
}

bool EnhancedFreeDrawTool::hasBrushStroke() const
{
    return m_brushRenderer.hasStroke();
}

void EnhancedFreeDrawTool::setBasePen(const QPen& pen)
{
    m_brushRenderer.setBasePen(pen);
}

BrushStrokeRenderer::BrushConfig EnhancedFreeDrawTool::getDefaultBrushConfig()
{
    BrushStrokeRenderer::BrushConfig config;
    config.minWidth = 2.0;
    config.maxWidth = 25.0;
    config.velocityFactor = 0.7;    // 速度对线宽的影响
    config.pressureFactor = 0.5;    // 压感对线宽的影响
    config.smoothingFactor = 0.6;   // 平滑程度
    config.maxPoints = 800;         // 最大点数
    config.enableFeathering = true; // 启用羽化效果
    return config;
}
