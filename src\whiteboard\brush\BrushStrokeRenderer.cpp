#include "BrushStrokeRenderer.h"
#include "../utils/FeatheringRenderer.h"
#include <QtMath>
#include <QDebug>

BrushStrokeRenderer::BrushStrokeRenderer()
{
    m_timer.start();
}

void BrushStrokeRenderer::startStroke(const QPointF& startPoint, qreal pressure)
{
    m_strokePoints.clear();
    m_isStroking = true;
    m_pathNeedsRebuild = true;
    m_boundsValid = false;
    
    // 创建起始点
    BrushPoint startBrushPoint;
    startBrushPoint.position = startPoint;
    startBrushPoint.pressure = qBound(0.1, pressure, 1.0);
    startBrushPoint.velocity = 0.0;
    startBrushPoint.timestamp = m_timer.elapsed();
    startBrushPoint.width = calculateDynamicWidth(startPoint, startBrushPoint.pressure);
    
    m_strokePoints.append(startBrushPoint);
}

void BrushStrokeRenderer::addStrokePoint(const QPointF& point, qreal pressure)
{
    if (!m_isStroking || m_strokePoints.isEmpty()) {
        return;
    }
    
    const BrushPoint& lastPoint = m_strokePoints.last();
    
    // 检查距离阈值，避免过密的点
    qreal distance = QLineF(lastPoint.position, point).length();
    if (distance < MIN_DISTANCE_THRESHOLD) {
        return;
    }
    
    // 计算时间间隔和速度
    qint64 currentTime = m_timer.elapsed();
    qint64 timeDelta = currentTime - lastPoint.timestamp;
    timeDelta = qMin(timeDelta, MAX_TIME_DELTA); // 限制最大时间间隔
    
    qreal velocity = calculateVelocity(point, lastPoint.position, timeDelta);
    
    // 创建新的笔锋点
    BrushPoint newPoint;
    newPoint.position = point;
    newPoint.pressure = qBound(0.1, pressure, 1.0);
    newPoint.velocity = velocity;
    newPoint.timestamp = currentTime;
    newPoint.width = calculateDynamicWidth(point, newPoint.pressure);
    
    m_strokePoints.append(newPoint);
    
    // 限制点数，避免内存过度使用
    if (m_strokePoints.size() > m_config.maxPoints) {
        m_strokePoints.removeFirst();
    }
    
    m_pathNeedsRebuild = true;
    m_boundsValid = false;
}

void BrushStrokeRenderer::finishStroke()
{
    if (!m_isStroking) {
        return;
    }
    
    m_isStroking = false;
    
    // 应用平滑算法
    if (m_strokePoints.size() > 2) {
        m_strokePoints = smoothStrokePoints(m_strokePoints);
    }
    
    m_pathNeedsRebuild = true;
    m_boundsValid = false;
}

void BrushStrokeRenderer::cancelStroke()
{
    m_strokePoints.clear();
    m_isStroking = false;
    m_pathNeedsRebuild = true;
    m_boundsValid = false;
    m_cachedPath = QPainterPath();
}

qreal BrushStrokeRenderer::calculateDynamicWidth(const QPointF& currentPoint, qreal pressure)
{
    Q_UNUSED(currentPoint)
    
    // 基础宽度来自画笔
    qreal baseWidth = m_basePen.widthF();
    if (baseWidth <= 0) {
        baseWidth = m_config.minWidth;
    }
    
    // 速度影响：快速绘制时线条变细
    qreal velocityEffect = 1.0;
    if (!m_strokePoints.isEmpty()) {
        const BrushPoint& lastPoint = m_strokePoints.last();
        qreal normalizedVelocity = qBound(0.0, lastPoint.velocity / MAX_VELOCITY, 1.0);
        velocityEffect = 1.0 - (normalizedVelocity * m_config.velocityFactor);
    }
    
    // 压感影响：压感越大线条越粗
    qreal pressureEffect = 0.5 + (pressure * m_config.pressureFactor);
    
    // 计算最终宽度
    qreal finalWidth = baseWidth * velocityEffect * pressureEffect;
    
    // 限制在配置范围内
    return qBound(m_config.minWidth, finalWidth, m_config.maxWidth);
}

qreal BrushStrokeRenderer::calculateVelocity(const QPointF& currentPoint, const QPointF& lastPoint, qint64 timeDelta)
{
    if (timeDelta <= 0) {
        return 0.0;
    }
    
    qreal distance = QLineF(lastPoint, currentPoint).length();
    qreal velocity = distance / (timeDelta / 1000.0); // 像素/秒
    
    return qMin(velocity, MAX_VELOCITY);
}

QPainterPath BrushStrokeRenderer::getCurrentStrokePath() const
{
    if (m_pathNeedsRebuild) {
        m_cachedPath = buildStrokePath();
        m_pathNeedsRebuild = false;
    }
    return m_cachedPath;
}

QRectF BrushStrokeRenderer::getCurrentBounds() const
{
    if (!m_boundsValid) {
        m_cachedBounds = getCurrentStrokePath().boundingRect();
        m_boundsValid = true;
    }
    return m_cachedBounds;
}

QPainterPath BrushStrokeRenderer::buildStrokePath() const
{
    if (m_strokePoints.size() < 2) {
        QPainterPath path;
        if (!m_strokePoints.isEmpty()) {
            const BrushPoint& point = m_strokePoints.first();
            path.addEllipse(point.position, point.width/2, point.width/2);
        }
        return path;
    }
    
    return createVariableWidthPath();
}

QPainterPath BrushStrokeRenderer::createVariableWidthPath() const
{
    QPainterPath strokePath;
    
    if (m_strokePoints.size() < 2) {
        return strokePath;
    }
    
    // 为每个线段创建变宽的路径
    for (int i = 0; i < m_strokePoints.size() - 1; ++i) {
        const BrushPoint& p1 = m_strokePoints[i];
        const BrushPoint& p2 = m_strokePoints[i + 1];
        
        // 计算线段方向和法向量
        QPointF direction = p2.position - p1.position;
        qreal length = QLineF(p1.position, p2.position).length();
        if (length < 0.1) continue;
        
        direction /= length;
        QPointF normal(-direction.y(), direction.x());
        
        // 创建四边形路径表示变宽线段
        QPointF p1Left = p1.position + normal * (p1.width / 2);
        QPointF p1Right = p1.position - normal * (p1.width / 2);
        QPointF p2Left = p2.position + normal * (p2.width / 2);
        QPointF p2Right = p2.position - normal * (p2.width / 2);
        
        QPainterPath segmentPath;
        segmentPath.moveTo(p1Left);
        segmentPath.lineTo(p2Left);
        segmentPath.lineTo(p2Right);
        segmentPath.lineTo(p1Right);
        segmentPath.closeSubpath();
        
        strokePath = strokePath.united(segmentPath);
    }
    
    return strokePath;
}

QVector<BrushStrokeRenderer::BrushPoint> BrushStrokeRenderer::smoothStrokePoints(const QVector<BrushPoint>& points) const
{
    if (points.size() < 3) {
        return points;
    }
    
    QVector<BrushPoint> smoothed;
    smoothed.reserve(points.size());
    
    // 保持第一个点
    smoothed.append(points.first());
    
    // 对中间点进行平滑
    for (int i = 1; i < points.size() - 1; ++i) {
        const BrushPoint& prev = points[i - 1];
        const BrushPoint& curr = points[i];
        const BrushPoint& next = points[i + 1];
        
        BrushPoint smoothPoint = curr;
        
        // 位置平滑
        qreal factor = m_config.smoothingFactor;
        smoothPoint.position = curr.position * (1.0 - factor) + 
                              (prev.position + next.position) * 0.5 * factor;
        
        // 宽度平滑
        smoothPoint.width = curr.width * (1.0 - factor) + 
                           (prev.width + next.width) * 0.5 * factor;
        
        smoothed.append(smoothPoint);
    }
    
    // 保持最后一个点
    smoothed.append(points.last());
    
    return smoothed;
}

BrushStrokeRenderer::BrushPoint BrushStrokeRenderer::interpolatePoint(const BrushPoint& p1, const BrushPoint& p2, qreal t) const
{
    BrushPoint result;
    result.position = p1.position + (p2.position - p1.position) * t;
    result.width = p1.width + (p2.width - p1.width) * t;
    result.pressure = p1.pressure + (p2.pressure - p1.pressure) * t;
    result.velocity = p1.velocity + (p2.velocity - p1.velocity) * t;
    result.timestamp = p1.timestamp + (p2.timestamp - p1.timestamp) * t;
    return result;
}

void BrushStrokeRenderer::drawBrushStroke(QPainter* painter, const QPainterPath& strokePath,
                                        const QPen& basePen, const QBrush& brush,
                                        const BrushConfig& config)
{
    if (strokePath.isEmpty() || !painter) {
        return;
    }

    painter->save();

    // 设置基础画笔和画刷
    QPen pen = basePen;
    pen.setCapStyle(Qt::RoundCap);
    pen.setJoinStyle(Qt::RoundJoin);

    painter->setPen(pen);
    painter->setBrush(brush);

    // 绘制笔锋路径
    painter->drawPath(strokePath);

    painter->restore();
}

void BrushStrokeRenderer::drawBrushStrokeWithFeathering(QPainter* painter, const QPainterPath& strokePath,
                                                      const QPen& basePen, const QBrush& brush,
                                                      const BrushConfig& brushConfig, ToolType toolType)
{
    if (strokePath.isEmpty() || !painter) {
        return;
    }

    // 如果启用羽化效果，使用FeatheringRenderer
    if (brushConfig.enableFeathering) {
        FeatheringRenderer::drawPathWithFeathering(painter, strokePath, basePen, brush, toolType);
    } else {
        // 否则使用普通笔锋渲染
        drawBrushStroke(painter, strokePath, basePen, brush, brushConfig);
    }
}
