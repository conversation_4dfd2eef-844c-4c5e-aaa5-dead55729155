#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>

#include "../src/whiteboard/core/WhiteBoard.h"
#include "../src/whiteboard/tools/enhanced/EnhancedFreeDrawTool.h"
#include "../src/whiteboard/brush/BrushStrokeRenderer.h"

/**
 * @brief 笔锋功能演示程序
 * 
 * 这个示例展示了如何使用whiteboard的笔锋功能：
 * 1. 基本的笔锋绘制
 * 2. 实时参数调节
 * 3. 不同效果预设
 */
class BrushStrokeExample : public QMainWindow
{
    Q_OBJECT

public:
    explicit BrushStrokeExample(QWidget* parent = nullptr)
        : QMainWindow(parent)
    {
        setupUI();
        setupWhiteBoard();
        connectSignals();
        
        // 设置默认为笔锋工具
        m_whiteBoard->setCurrentTool(ToolType::EnhancedFreeDraw);
        
        // 应用默认配置
        applyBrushConfig();
    }

private slots:
    void onParameterChanged()
    {
        applyBrushConfig();
    }
    
    void onPresetSelected()
    {
        QPushButton* button = qobject_cast<QPushButton*>(sender());
        if (!button) return;
        
        QString preset = button->text();
        
        if (preset == "书法") {
            // 书法效果：高速度敏感，高压感敏感
            m_velocitySlider->setValue(90);
            m_pressureSlider->setValue(80);
            m_smoothingSlider->setValue(50);
            m_minWidthSpin->setValue(1.0);
            m_maxWidthSpin->setValue(25.0);
        } else if (preset == "绘画") {
            // 绘画效果：中等速度敏感，低压感敏感，高平滑
            m_velocitySlider->setValue(60);
            m_pressureSlider->setValue(40);
            m_smoothingSlider->setValue(80);
            m_minWidthSpin->setValue(3.0);
            m_maxWidthSpin->setValue(15.0);
        } else if (preset == "签名") {
            // 签名效果：中高速度敏感，中等压感敏感
            m_velocitySlider->setValue(70);
            m_pressureSlider->setValue(60);
            m_smoothingSlider->setValue(60);
            m_minWidthSpin->setValue(2.0);
            m_maxWidthSpin->setValue(12.0);
        }
        
        applyBrushConfig();
    }
    
    void onClearCanvas()
    {
        // 清空画布
        if (m_whiteBoard && m_whiteBoard->getScene()) {
            m_whiteBoard->getScene()->clearAllItems();
        }
    }

private:
    void setupUI()
    {
        setWindowTitle("笔锋功能演示 - WhiteBoard");
        setMinimumSize(1200, 800);
        
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QHBoxLayout* mainLayout = new QHBoxLayout(centralWidget);
        
        // 左侧控制面板
        QWidget* controlPanel = createControlPanel();
        controlPanel->setFixedWidth(300);
        mainLayout->addWidget(controlPanel);
        
        // 右侧白板区域
        m_whiteBoardContainer = new QWidget();
        m_whiteBoardContainer->setStyleSheet("background-color: white; border: 1px solid gray;");
        mainLayout->addWidget(m_whiteBoardContainer, 1);
    }
    
    QWidget* createControlPanel()
    {
        QWidget* panel = new QWidget();
        QVBoxLayout* layout = new QVBoxLayout(panel);
        
        // 预设效果
        QGroupBox* presetGroup = new QGroupBox("预设效果");
        QVBoxLayout* presetLayout = new QVBoxLayout(presetGroup);
        
        QPushButton* calligraphyBtn = new QPushButton("书法");
        QPushButton* paintingBtn = new QPushButton("绘画");
        QPushButton* signatureBtn = new QPushButton("签名");
        
        presetLayout->addWidget(calligraphyBtn);
        presetLayout->addWidget(paintingBtn);
        presetLayout->addWidget(signatureBtn);
        
        connect(calligraphyBtn, &QPushButton::clicked, this, &BrushStrokeExample::onPresetSelected);
        connect(paintingBtn, &QPushButton::clicked, this, &BrushStrokeExample::onPresetSelected);
        connect(signatureBtn, &QPushButton::clicked, this, &BrushStrokeExample::onPresetSelected);
        
        layout->addWidget(presetGroup);
        
        // 参数调节
        QGroupBox* paramGroup = new QGroupBox("参数调节");
        QVBoxLayout* paramLayout = new QVBoxLayout(paramGroup);
        
        // 速度因子
        paramLayout->addWidget(new QLabel("速度敏感度:"));
        m_velocitySlider = new QSlider(Qt::Horizontal);
        m_velocitySlider->setRange(0, 100);
        m_velocitySlider->setValue(80);
        m_velocityLabel = new QLabel("0.8");
        QHBoxLayout* velocityLayout = new QHBoxLayout();
        velocityLayout->addWidget(m_velocitySlider);
        velocityLayout->addWidget(m_velocityLabel);
        paramLayout->addLayout(velocityLayout);
        
        // 压感因子
        paramLayout->addWidget(new QLabel("压感敏感度:"));
        m_pressureSlider = new QSlider(Qt::Horizontal);
        m_pressureSlider->setRange(0, 100);
        m_pressureSlider->setValue(60);
        m_pressureLabel = new QLabel("0.6");
        QHBoxLayout* pressureLayout = new QHBoxLayout();
        pressureLayout->addWidget(m_pressureSlider);
        pressureLayout->addWidget(m_pressureLabel);
        paramLayout->addLayout(pressureLayout);
        
        // 平滑因子
        paramLayout->addWidget(new QLabel("平滑程度:"));
        m_smoothingSlider = new QSlider(Qt::Horizontal);
        m_smoothingSlider->setRange(0, 100);
        m_smoothingSlider->setValue(70);
        m_smoothingLabel = new QLabel("0.7");
        QHBoxLayout* smoothingLayout = new QHBoxLayout();
        smoothingLayout->addWidget(m_smoothingSlider);
        smoothingLayout->addWidget(m_smoothingLabel);
        paramLayout->addLayout(smoothingLayout);
        
        // 线宽范围
        paramLayout->addWidget(new QLabel("最小线宽:"));
        m_minWidthSpin = new QDoubleSpinBox();
        m_minWidthSpin->setRange(0.5, 10.0);
        m_minWidthSpin->setValue(2.0);
        m_minWidthSpin->setSingleStep(0.5);
        paramLayout->addWidget(m_minWidthSpin);
        
        paramLayout->addWidget(new QLabel("最大线宽:"));
        m_maxWidthSpin = new QDoubleSpinBox();
        m_maxWidthSpin->setRange(5.0, 50.0);
        m_maxWidthSpin->setValue(20.0);
        m_maxWidthSpin->setSingleStep(1.0);
        paramLayout->addWidget(m_maxWidthSpin);
        
        // 羽化效果
        m_featheringCheck = new QCheckBox("启用羽化效果");
        m_featheringCheck->setChecked(true);
        paramLayout->addWidget(m_featheringCheck);
        
        layout->addWidget(paramGroup);
        
        // 操作按钮
        QGroupBox* actionGroup = new QGroupBox("操作");
        QVBoxLayout* actionLayout = new QVBoxLayout(actionGroup);
        
        QPushButton* clearBtn = new QPushButton("清空画布");
        connect(clearBtn, &QPushButton::clicked, this, &BrushStrokeExample::onClearCanvas);
        actionLayout->addWidget(clearBtn);
        
        layout->addWidget(actionGroup);
        
        layout->addStretch();
        
        return panel;
    }
    
    void setupWhiteBoard()
    {
        m_whiteBoard = new WhiteBoard(this);
        m_whiteBoard->initialize();
        
        // 将白板视图添加到容器中
        QVBoxLayout* containerLayout = new QVBoxLayout(m_whiteBoardContainer);
        containerLayout->setContentsMargins(0, 0, 0, 0);
        containerLayout->addWidget(m_whiteBoard->getView());
    }
    
    void connectSignals()
    {
        connect(m_velocitySlider, &QSlider::valueChanged, this, &BrushStrokeExample::onParameterChanged);
        connect(m_pressureSlider, &QSlider::valueChanged, this, &BrushStrokeExample::onParameterChanged);
        connect(m_smoothingSlider, &QSlider::valueChanged, this, &BrushStrokeExample::onParameterChanged);
        connect(m_minWidthSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &BrushStrokeExample::onParameterChanged);
        connect(m_maxWidthSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &BrushStrokeExample::onParameterChanged);
        connect(m_featheringCheck, &QCheckBox::toggled, this, &BrushStrokeExample::onParameterChanged);
    }
    
    void applyBrushConfig()
    {
        // 更新标签显示
        qreal velocity = m_velocitySlider->value() / 100.0;
        qreal pressure = m_pressureSlider->value() / 100.0;
        qreal smoothing = m_smoothingSlider->value() / 100.0;
        
        m_velocityLabel->setText(QString::number(velocity, 'f', 2));
        m_pressureLabel->setText(QString::number(pressure, 'f', 2));
        m_smoothingLabel->setText(QString::number(smoothing, 'f', 2));
        
        // 获取笔锋工具并应用配置
        ShapeToolManager* manager = ShapeToolManager::instance();
        if (manager && manager->hasToolType(ToolType::EnhancedFreeDraw)) {
            EnhancedFreeDrawTool* brushTool = static_cast<EnhancedFreeDrawTool*>(
                manager->getTool(ToolType::EnhancedFreeDraw));
            
            if (brushTool) {
                BrushStrokeRenderer::BrushConfig config;
                config.velocityFactor = velocity;
                config.pressureFactor = pressure;
                config.smoothingFactor = smoothing;
                config.minWidth = m_minWidthSpin->value();
                config.maxWidth = m_maxWidthSpin->value();
                config.enableFeathering = m_featheringCheck->isChecked();
                config.maxPoints = 800;
                
                brushTool->setBrushConfig(config);
            }
        }
    }

private:
    WhiteBoard* m_whiteBoard = nullptr;
    QWidget* m_whiteBoardContainer = nullptr;
    
    // 控制组件
    QSlider* m_velocitySlider = nullptr;
    QSlider* m_pressureSlider = nullptr;
    QSlider* m_smoothingSlider = nullptr;
    QDoubleSpinBox* m_minWidthSpin = nullptr;
    QDoubleSpinBox* m_maxWidthSpin = nullptr;
    QCheckBox* m_featheringCheck = nullptr;
    
    // 显示标签
    QLabel* m_velocityLabel = nullptr;
    QLabel* m_pressureLabel = nullptr;
    QLabel* m_smoothingLabel = nullptr;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    BrushStrokeExample window;
    window.show();
    
    return app.exec();
}

#include "BrushStrokeExample.moc"
