#ifndef BRUSHSTROKERENDERER_H
#define BRUSHSTROKERENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QPointF>
#include <QVector>
#include <QElapsedTimer>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 笔锋渲染器 - 提供基于压感和速度的动态线宽笔锋效果
 * 
 * 核心功能：
 * 1. 基于绘制速度的动态线宽计算
 * 2. 模拟压感的笔锋效果
 * 3. 与现有FeatheringRenderer完美集成
 * 4. 高性能的笔锋路径构建
 */
class BrushStrokeRenderer
{
public:
    /**
     * @brief 笔锋点数据结构
     */
    struct BrushPoint {
        QPointF position;       // 点位置
        qreal width;           // 线宽
        qreal pressure;        // 压感值 (0.0-1.0)
        qreal velocity;        // 速度
        qint64 timestamp;      // 时间戳
        
        BrushPoint() : width(1.0), pressure(1.0), velocity(0.0), timestamp(0) {}
        BrushPoint(const QPointF& pos, qreal w = 1.0, qreal p = 1.0) 
            : position(pos), width(w), pressure(p), velocity(0.0), timestamp(0) {}
    };

    /**
     * @brief 笔锋配置参数
     */
    struct BrushConfig {
        qreal minWidth = 1.0;           // 最小线宽
        qreal maxWidth = 20.0;          // 最大线宽
        qreal velocityFactor = 0.8;     // 速度影响因子 (0.0-1.0)
        qreal pressureFactor = 0.6;     // 压感影响因子 (0.0-1.0)
        qreal smoothingFactor = 0.7;    // 平滑因子 (0.0-1.0)
        int maxPoints = 1000;           // 最大点数限制
        bool enableFeathering = true;   // 是否启用羽化效果
    };

public:
    BrushStrokeRenderer();
    ~BrushStrokeRenderer() = default;

    // 配置接口
    void setBrushConfig(const BrushConfig& config) { m_config = config; }
    BrushConfig getBrushConfig() const { return m_config; }
    void setBasePen(const QPen& pen) { m_basePen = pen; }

    // 笔锋路径构建
    void startStroke(const QPointF& startPoint, qreal pressure = 1.0);
    void addStrokePoint(const QPointF& point, qreal pressure = 1.0);
    void finishStroke();
    void cancelStroke();

    // 路径获取
    QPainterPath getCurrentStrokePath() const;
    QRectF getCurrentBounds() const;
    bool hasStroke() const { return !m_strokePoints.isEmpty(); }

    // 渲染接口
    static void drawBrushStroke(QPainter* painter, const QPainterPath& strokePath,
                               const QPen& basePen, const QBrush& brush, 
                               const BrushConfig& config);

    // 与FeatheringRenderer集成
    static void drawBrushStrokeWithFeathering(QPainter* painter, const QPainterPath& strokePath,
                                            const QPen& basePen, const QBrush& brush,
                                            const BrushConfig& brushConfig, ToolType toolType);

private:
    // 核心算法
    qreal calculateDynamicWidth(const QPointF& currentPoint, qreal pressure);
    qreal calculateVelocity(const QPointF& currentPoint, const QPointF& lastPoint, qint64 timeDelta);
    QPainterPath buildStrokePath() const;
    QPainterPath createVariableWidthPath() const;
    
    // 平滑算法
    QVector<BrushPoint> smoothStrokePoints(const QVector<BrushPoint>& points) const;
    BrushPoint interpolatePoint(const BrushPoint& p1, const BrushPoint& p2, qreal t) const;

    // 数据成员
    BrushConfig m_config;
    QPen m_basePen;
    QVector<BrushPoint> m_strokePoints;
    QElapsedTimer m_timer;
    bool m_isStroking = false;
    
    // 缓存
    mutable QPainterPath m_cachedPath;
    mutable bool m_pathNeedsRebuild = true;
    mutable QRectF m_cachedBounds;
    mutable bool m_boundsValid = false;

    // 性能优化
    static constexpr qreal MIN_DISTANCE_THRESHOLD = 2.0;  // 最小距离阈值
    static constexpr qreal MAX_VELOCITY = 1000.0;         // 最大速度限制
    static constexpr qint64 MAX_TIME_DELTA = 100;         // 最大时间间隔(ms)
};

#endif // BRUSHSTROKERENDERER_H
